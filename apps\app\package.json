{"name": "@roasmax/app", "version": "0.0.1", "private": true, "scripts": {"dev:watch": "tsc --watch --noEmit --preserveWatchOutput && next dev", "dev": "next dev", "build": "next build && rm -f .next/standalone/.env", "start": "next start", "serve": "next build && next start", "lint": "eslint src --fix --ext .ts,.tsx,.js,.jsx --max-warnings 0", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dismissable-layer": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.0", "@roasmax/database": "workspace:*", "@roasmax/serve": "workspace:*", "@roasmax/utils": "workspace:*", "@types/js-cookie": "^3.0.6", "ai": "^4.1.24", "async-mutex": "^0.5.0", "authing-node-sdk": "^4.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "cors": "^2.8.5", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "framer-motion": "^11.11.9", "html2canvas": "^1.4.1", "immer": "^10.0.3", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.0", "jszip": "^3.10.1", "langfuse": "^3.19.0", "lodash": "^4.17.21", "lucide-react": "^0.453.0", "moment": "^2.30.1", "mp4box": "^0.5.3", "next": "^14.2.6", "node-snowflake": "^0.0.1", "nookies": "^2.5.2", "p-limit": "^6.2.0", "query-string": "^9.0.0", "react": "^18.3.1", "react-day-picker": "^8.7.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.3", "react-player": "^2.16.0", "react-resizable-panels": "^2.1.4", "react-router-dom": "^6.26.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "server-only": "^0.0.1", "sharp": "^0.33.5", "swiper": "^11.2.2", "swr": "^2.2.5", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "tencentcloud-sdk-nodejs-tmt": "^4.0.975", "tencentcloud-sdk-nodejs-vod": "^4.0.891", "vaul": "^1.1.0", "vod-js-sdk-v6": "^1.7.1-beta.1", "xlsx": "^0.18.5", "zod": "^3.23.8", "zustand": "^4.5.4"}, "devDependencies": {"@commitlint/cli": "^19.0.3", "@commitlint/config-conventional": "^19.0.3", "@next/bundle-analyzer": "^14.1.3", "@next/swc-linux-x64-gnu": "^14.2.6", "@roasmax/eslint-config": "workspace:*", "@roasmax/typescript-config": "workspace:*", "@tailwindcss/typography": "^0.5.16", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.6", "@types/lodash": "^4.17.12", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.18", "commander": "^12.1.0", "cross-env": "^7.0.3", "husky": "^8.0.3", "lint-staged": "^15.2.2", "postcss": "^8.4.37", "prettier-plugin-tailwindcss": "^0.6.8", "tailwindcss": "^3.4.1", "webpack": "^5.93.0"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx,json}": ["npm run lint", "prettier --write"]}}