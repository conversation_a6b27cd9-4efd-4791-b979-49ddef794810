# 移除 Authing 依赖方案

## 概述

本文档描述了从项目中移除 Authing 依赖的完整方案，采用**数据迁移 + 密码重置**的策略，确保用户数据安全和系统平滑过渡。

## 当前 Authing 依赖分析

### 主要依赖点

1. **包依赖**: `packages/serve/package.json` 中的 `authing-node-sdk: ^4.0.0`
2. **认证插件**: `packages/serve/src/context/plugins/authing.ts`
3. **登录接口**: `apps/app/src/app/api/login/route.ts`
4. **用户相关操作**: `apps/app/src/services/actions/me.ts`
5. **环境变量**: `APPID`, `APPSECRET`, `APPHOST` 等

### 现有优势

- ✅ 项目已有 `members` 表，包含用户密码字段
- ✅ 已有自定义认证实现 `packages/serve/src/auth/custom-auth.ts`
- ✅ 部分功能已在使用 `members` 表（如 webhook）
- ✅ JWT 解析逻辑可复用

## 迁移方案：数据迁移 + 密码重置
暂不需要

## 第二阶段：数据迁移

暂不需要

## 第三阶段：代码修改

### 3.1 替换认证插件

**文件**: `packages/serve/src/context/plugins/authing.ts`

```typescript
import { CustomAuthenticationClient } from '../../auth/custom-auth';
import { ActionContextPluginLoader } from '../../types';

type AuthingPlugin = CustomAuthenticationClient;

const authingPlugin: ActionContextPluginLoader = (context) => {
  if (!process.env.APPID || !process.env.APPSECRET || !process.env.APPHOST) {
    throw new Error('APPID, APPSECRET, or APPHOST is not set');
  }
  
  const authClient = new CustomAuthenticationClient({
    appId: process.env.APPID,
    appSecret: process.env.APPSECRET,
    appHost: process.env.APPHOST,
  });
  authClient.setAccessToken(context.token);

  return {
    name: 'authing',
    plugin: authClient,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    authing: AuthingPlugin;
  }
}

export default authingPlugin;
```

### 3.2 修改登录接口

**文件**: `apps/app/src/app/api/login/route.ts`

```typescript
import { CustomAuthenticationClient } from '@roasmax/serve/auth/custom-auth';
import { NextResponse } from 'next/server';
import { getLoginSessionInfo } from '@/utils/authing';
import * as jose from 'jose';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { account: email, password } = body;

    if (!email || !password) {
      return NextResponse.json({ message: '邮箱和密码不能为空' }, { status: 400 });
    }

    const authenticationClient = new CustomAuthenticationClient({
      appId: process.env.APPID!,
      appSecret: process.env.APPSECRET!,
      appHost: process.env.APPHOST!,
    });

    const result = await authenticationClient.signInByEmailPassword({
      email,
      password,
    });

    if (result.statusCode === 200) {
      const { data: loginData } = result;
      const { id_token } = loginData;
      
      // 检查是否需要重置密码
      if (loginData.needPasswordReset) {
        return NextResponse.json({
          message: '首次登录需要重置密码',
          code: 'NEED_PASSWORD_RESET',
          data: { email }
        }, { status: 200 });
      }

      const response = NextResponse.json({ 
        data: loginData, 
        code: 200, 
        message: '登录成功' 
      });
      
      response.cookies.set('token', id_token);
      response.cookies.set('Authorization', id_token);
      
      return response;
    } else {
      return NextResponse.json(
        { message: result.message || '登录失败', code: result.statusCode },
        { status: result.statusCode },
      );
    }
  } catch (error: any) {
    return NextResponse.json(
      { message: error.message || '服务器错误', code: 500 },
      { status: 500 }
    );
  }
}
```

### 3.3 完善自定义认证客户端

**文件**: `packages/serve/src/auth/custom-auth.ts`

需要完善以下功能：
- 连接真实数据库进行用户验证
- 实现密码加密和验证（使用 bcrypt）
- 处理密码重置逻辑
- 实现用户信息更新

### 3.4 移除包依赖

**文件**: `packages/serve/package.json`

```json
{
  "dependencies": {
    // 移除这一行
    // "authing-node-sdk": "^4.0.0",
    
    // 添加密码加密库
    "bcryptjs": "^2.4.3"
  },
  "devDependencies": {
    "@types/bcryptjs": "^2.4.2"
  }
}
```

## 第四阶段：密码重置功能

### 4.1 创建密码重置页面

**文件**: `apps/app/src/app/reset-password/page.tsx`

### 4.2 实现密码重置 API

**文件**: `apps/app/src/app/api/reset-password/route.ts`

### 4.3 添加密码验证逻辑

实现安全的密码验证：
- 密码加密存储

## 测试计划

### 功能测试

- [ ] 用户登录功能
- [ ] 密码重置功能
- [ ] 用户信息获取
- [ ] 用户信息更新
- [ ] 密码修改功能

### 性能测试

- [ ] 登录接口性能
- [ ] 并发登录测试
- [ ] 数据库查询性能

### 安全测试

- [ ] 密码加密验证
- [ ] JWT Token 安全性
- [ ] 权限验证
