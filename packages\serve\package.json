{"name": "@roasmax/serve", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"dev": "tsup --watch", "build": "tsup", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@roasmax/database": "workspace:*", "@roasmax/utils": "workspace:*", "axios": "^1.7.9", "bcryptjs": "^3.0.2", "chalk": "^5.3.0", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "langfuse": "^3.19.0", "mysql2": "^3.11.4", "next": "^14.2.6", "tencentcloud-sdk-nodejs": "^4.0.975"}, "devDependencies": {"@roasmax/eslint-config": "workspace:*", "@roasmax/typescript-config": "workspace:*", "@types/jsonwebtoken": "^9.0.6"}}