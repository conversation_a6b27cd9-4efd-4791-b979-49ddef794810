import { CustomAuthenticationClient } from '../../auth/custom-auth';
import { ActionContextPluginLoader } from '../../types';

type AuthingPlugin = CustomAuthenticationClient;

const authingPlugin: ActionContextPluginLoader = (context) => {
  if (!process.env.APPID || !process.env.APPSECRET || !process.env.APPHOST) {
    throw new Error('APPID, APPSECRET, or APPHOST is not set');
  }

  const authClient = new CustomAuthenticationClient({
    appId: process.env.APPID,
    appSecret: process.env.APPSECRET,
    appHost: process.env.APPHOST,
  });
  authClient.setAccessToken(context.token);

  return {
    name: 'authing',
    plugin: authClient,
  };
};

declare module '@roasmax/serve' {
  interface ActionContextPlugins<T = any> {
    /**
     * authing AuthenticationClient 操作API
     */
    authing: AuthingPlugin;
  }
}

export default authingPlugin;
