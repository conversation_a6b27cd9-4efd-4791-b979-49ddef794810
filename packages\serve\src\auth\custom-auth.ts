import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import * as jose from 'jose';
import { prisma } from '../prisma';

export interface AuthConfig {
  appId: string;
  appSecret: string;
  appHost: string;
}

export interface SignInByEmailPasswordParams {
  email: string;
  password: string;
}

export interface UpdateProfileParams {
  nickname?: string;
}

export interface UpdatePasswordParams {
  newPassword: string;
  oldPassword: string;
}

export interface AuthResult<T = any> {
  statusCode: number;
  message?: string;
  data?: T;
}

export interface LoginData {
  id_token: string;
  access_token?: string;
  refresh_token?: string;
  needPasswordReset?: boolean;
}

export interface UserProfile {
  id: string;
  nickname: string;
  username: string;
  email: string;
  user_id: string;
  tenant_id: string;
}

export class CustomAuthenticationClient {
  private config: AuthConfig;
  private accessToken: string = '';

  constructor(config: AuthConfig) {
    this.config = config;
  }

  setAccessToken(token: string) {
    this.accessToken = token;
  }

  /**
   * 通过邮箱密码登录
   */
  async signInByEmailPassword(params: SignInByEmailPasswordParams): Promise<AuthResult<LoginData>> {
    try {
      const { email, password } = params;

      // 查找用户
      const user = await prisma.members.findFirst({
        where: {
          email: email,
          tmp_deleted_at: null,
        },
      });

      if (!user) {
        return {
          statusCode: 400,
          message: '用户不存在',
        };
      }

      // 验证密码
      let isPasswordValid = false;
      
      // 如果有加密密码，使用bcrypt验证
      if (user.encrypted_password) {
        isPasswordValid = await bcrypt.compare(password, user.encrypted_password);
      } else if (user.password) {
        // 如果只有明文密码，直接比较（兼容旧数据）
        isPasswordValid = user.password === password;
        
        // 如果验证成功，将明文密码加密存储
        if (isPasswordValid) {
          const hashedPassword = await bcrypt.hash(password, 10);
          await prisma.members.update({
            where: { id: user.id },
            data: { encrypted_password: hashedPassword },
          });
        }
      }

      if (!isPasswordValid) {
        return {
          statusCode: 400,
          message: '密码错误',
        };
      }

      // 生成JWT token
      const tokenPayload = {
        sub: user.user_id,
        email: user.email,
        nickname: user.nickname,
        username: user.account,
        userpool_id: user.tenant_id,
        aud: this.config.appId,
        iss: this.config.appHost,
        data: {
          type: 'user',
          userPoolId: user.tenant_id,
          appId: this.config.appId,
          id: user.id,
          userId: user.user_id,
          _id: user.id,
          phone: null,
          email: user.email,
          username: user.account,
          unionid: null,
          openid: null,
          clientId: this.config.appId,
        },
      };

      const id_token = await new jose.SignJWT(tokenPayload)
        .setProtectedHeader({ alg: 'HS256' })
        .setExpirationTime('24h')
        .setIssuedAt()
        .sign(new TextEncoder().encode(this.config.appSecret));

      return {
        statusCode: 200,
        data: {
          id_token,
          needPasswordReset: !user.encrypted_password, // 如果没有加密密码，需要重置
        },
      };
    } catch (error: any) {
      console.error('Login error:', error);
      return {
        statusCode: 500,
        message: error.message || '登录失败',
      };
    }
  }

  /**
   * 获取用户信息
   */
  async getProfile(options?: { withCustomData?: boolean; withIdentities?: boolean; withDepartmentIds?: boolean }): Promise<AuthResult<UserProfile>> {
    try {
      if (!this.accessToken) {
        return {
          statusCode: 401,
          message: '未授权',
        };
      }

      // 解析token获取用户信息
      const decoded = await this.verifyToken(this.accessToken);
      if (!decoded) {
        return {
          statusCode: 401,
          message: 'Token无效',
        };
      }

      // 从数据库获取最新用户信息
      const user = await prisma.members.findFirst({
        where: {
          user_id: decoded.sub,
          tmp_deleted_at: null,
        },
      });

      if (!user) {
        return {
          statusCode: 404,
          message: '用户不存在',
        };
      }

      return {
        statusCode: 200,
        data: {
          id: user.id,
          nickname: user.nickname,
          username: user.account,
          email: user.email,
          user_id: user.user_id,
          tenant_id: user.tenant_id,
        },
      };
    } catch (error: any) {
      console.error('Get profile error:', error);
      return {
        statusCode: 500,
        message: error.message || '获取用户信息失败',
      };
    }
  }

  /**
   * 更新用户信息
   */
  async updateProfile(params: UpdateProfileParams): Promise<AuthResult<boolean>> {
    try {
      if (!this.accessToken) {
        return {
          statusCode: 401,
          message: '未授权',
        };
      }

      const decoded = await this.verifyToken(this.accessToken);
      if (!decoded) {
        return {
          statusCode: 401,
          message: 'Token无效',
        };
      }

      await prisma.members.updateMany({
        where: {
          user_id: decoded.sub,
          tmp_deleted_at: null,
        },
        data: {
          nickname: params.nickname,
        },
      });

      return {
        statusCode: 200,
        data: true,
      };
    } catch (error: any) {
      console.error('Update profile error:', error);
      return {
        statusCode: 500,
        message: error.message || '更新用户信息失败',
      };
    }
  }

  /**
   * 更新密码
   */
  async updatePassword(params: UpdatePasswordParams): Promise<AuthResult<boolean>> {
    try {
      if (!this.accessToken) {
        return {
          statusCode: 401,
          message: '未授权',
        };
      }

      const decoded = await this.verifyToken(this.accessToken);
      if (!decoded) {
        return {
          statusCode: 401,
          message: 'Token无效',
        };
      }

      const user = await prisma.members.findFirst({
        where: {
          user_id: decoded.sub,
          tmp_deleted_at: null,
        },
      });

      if (!user) {
        return {
          statusCode: 404,
          message: '用户不存在',
        };
      }

      // 验证旧密码
      let isOldPasswordValid = false;
      if (user.encrypted_password) {
        isOldPasswordValid = await bcrypt.compare(params.oldPassword, user.encrypted_password);
      } else if (user.password) {
        isOldPasswordValid = user.password === params.oldPassword;
      }

      if (!isOldPasswordValid) {
        return {
          statusCode: 400,
          message: '旧密码错误',
        };
      }

      // 加密新密码
      const hashedNewPassword = await bcrypt.hash(params.newPassword, 10);

      // 更新密码
      await prisma.members.updateMany({
        where: {
          user_id: decoded.sub,
          tmp_deleted_at: null,
        },
        data: {
          encrypted_password: hashedNewPassword,
          password: params.newPassword, // 保留明文密码以兼容现有系统
        },
      });

      return {
        statusCode: 200,
        data: true,
      };
    } catch (error: any) {
      console.error('Update password error:', error);
      return {
        statusCode: 500,
        message: error.message || '更新密码失败',
      };
    }
  }

  /**
   * 撤销token（登出）
   */
  async revokeToken(token: string): Promise<boolean> {
    // 在实际应用中，可以将token加入黑名单
    // 这里简单返回true，表示撤销成功
    return true;
  }

  /**
   * 验证JWT token
   */
  private async verifyToken(token: string): Promise<any> {
    try {
      const cleanToken = token.replace('Bearer ', '');
      
      // 首先尝试使用 jsonwebtoken 验证
      try {
        const decoded = jwt.verify(cleanToken, this.config.appSecret);
        if (typeof decoded === 'string' || !decoded.exp) {
          throw new Error('解析失败');
        }
        const expired = Date.now() / 1000 > decoded.exp;
        if (!expired) {
          return decoded;
        }
      } catch (e: any) {
        console.error('jwt验证失败，尝试使用jose解析', e.message);
        
        // 使用 jose.decodeJwt 作为备选方案
        const decoded = jose.decodeJwt(cleanToken);
        if (!decoded.exp) {
          throw new Error('Token缺少过期时间');
        }
        
        const expired = Date.now() / 1000 > decoded.exp;
        if (!expired) {
          return decoded;
        }
      }
      
      return null;
    } catch (error: any) {
      console.error('Token verification error:', error);
      return null;
    }
  }
}
