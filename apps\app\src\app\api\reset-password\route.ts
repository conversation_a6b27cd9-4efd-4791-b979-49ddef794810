import { NextResponse } from 'next/server';
import { prisma } from '@/utils/prisma';
import bcrypt from 'bcryptjs';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { email, newPassword } = body;

    if (!email || !newPassword) {
      return NextResponse.json({ message: '邮箱和新密码不能为空' }, { status: 400 });
    }

    // 验证密码强度
    if (newPassword.length < 6) {
      return NextResponse.json({ message: '密码长度至少6位' }, { status: 400 });
    }

    // 查找用户
    const user = await prisma.members.findFirst({
      where: {
        email: email,
        tmp_deleted_at: null,
      },
    });

    if (!user) {
      return NextResponse.json({ message: '用户不存在' }, { status: 404 });
    }

    // 加密新密码
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // 更新密码
    await prisma.members.update({
      where: { id: user.id },
      data: {
        encrypted_password: hashedPassword,
        password: newPassword, // 保留明文密码以兼容现有系统
      },
    });

    return NextResponse.json({ 
      message: '密码重置成功',
      code: 200 
    });

  } catch (error: any) {
    console.error('Password reset error:', error);
    return NextResponse.json(
      { message: error.message || '密码重置失败', code: 500 },
      { status: 500 }
    );
  }
}
