import { CustomAuthenticationClient } from '@roasmax/serve';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { account: email, password } = body;

    if (!email || !password) {
      return NextResponse.json({ message: '邮箱和密码不能为空' }, { status: 400 });
    }
    const authenticationClient = new CustomAuthenticationClient({
      appId: process.env.APPID!,
      appSecret: process.env.APPSECRET!,
      appHost: process.env.APPHOST!,
    });

    const result = await authenticationClient.signInByEmailPassword({
      email,
      password,
    });

    if (result.statusCode === 200 && result.data) {
      const loginData = result.data;
      const { id_token } = loginData;

      // 检查是否需要重置密码
      if (loginData.needPasswordReset) {
        return NextResponse.json({
          message: '首次登录需要重置密码',
          code: 'NEED_PASSWORD_RESET',
          data: { email }
        }, { status: 200 });
      }

      const response = NextResponse.json({
        data: loginData,
        code: 200,
        message: '登录成功'
      });

      response.cookies.set('token', id_token);
      response.cookies.set('Authorization', id_token);

      return response;
    } else {
      return NextResponse.json(
        { message: result.message || '登录失败', code: result.statusCode },
        { status: result.statusCode },
      );
    }
  } catch (error) {
    console.error('登录错误:', error);
    return NextResponse.json({ message: '登录失败，请稍后重试', code: 500 }, { status: 500 });
  }
}
